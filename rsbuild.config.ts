import { defineConfig, loadEnv } from "@rsbuild/core";
import { pluginReact } from "@rsbuild/plugin-react";
import { pluginSass } from "@rsbuild/plugin-sass";
import path from "node:path";
import pkg from "./package.json";

const deps = pkg.dependencies;

const { publicVars } = loadEnv({ prefixes: ["REACT_APP_"] });

export default defineConfig({
  plugins: [pluginReact(), pluginSass()],
  tools: {
    rspack: {
      output: {
        uniqueName: "remote",
        publicPath: `${process.env.PUBLIC_URL || ""}/`,
      },
    },
  },
  performance: {
    bundleAnalyze: {
      analyzerMode: "static",
      openAnalyzer: false,
      reportFilename: `rs-build-report-${process.env.REACT_APP_ENV}.html`,
    },
    chunkSplit: {
      strategy: "split-by-module",
    },
  },
  html: {
    template: "./public/index.html",
    inject: "body",
  },
  dev: {
    assetPrefix: "http://localhost:3000/",
  },
  output: {
    assetPrefix: `${process.env.PUBLIC_URL || ""}/`,
    distPath: {
      root: process.env.BUILD_DIR,
    },
    cleanDistPath: true,
    sourceMap: {
      js: "source-map",
      css: true
    },
  },
  source: {
    entry: {
      index: "./src/index.tsx",
    },
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
    define: {
      ...publicVars,
      __DEV__: process.env.NODE_ENV === "development",
    },
  },
  server: {
    htmlFallback: "index",
  },
  moduleFederation: {
    options: {
      name: process.env.APP_NAME!,
      filename: "remoteEntry.js",
      exposes: {
        [process.env.APP_PATH!]: path.join(__dirname, "./src/zpi-app.tsx"),
      },
      shared: [
        {
          react: {
            shareKey: "react@18",
            singleton: true,
            requiredVersion: deps.react,
          },
          "react-dom": {
            shareKey: 'react-dom@18',
            singleton: true,
            requiredVersion: deps["react-dom"],
          },
        },
      ],
    },
  },
});
