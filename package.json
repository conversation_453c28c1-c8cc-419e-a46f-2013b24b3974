{"name": "installment-app", "version": "2.0.5", "private": true, "dependencies": {"@ce/survey-wrapper": "^1.0.7", "@hookform/resolvers": "^3.9.1", "@loadable/component": "^5.16.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-visually-hidden": "^1.1.0", "@rive-app/react-canvas": "^4.18.4", "@zpi/federation-adapter": "^0.1.2-alpha.5", "@zpi/looknfeel": "^0.40.2", "@zpi/looknfeel-icons": "^0.1.25", "buffer": "^6.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.2.0", "dompurify": "^3.2.5", "embla-carousel-autoplay": "^8.0.0-rc19", "embla-carousel-react": "^8.0.0-rc19", "lodash.debounce": "^4.0.8", "lodash.get": "^4.4.2", "lodash.isnumber": "^3.0.3", "mobile-detect": "^1.4.5", "motion": "^12.6.2", "pdfjs-dist": "3.9.179", "query-string": "^8.1.0", "react": "^18.3.1", "react-aria-components": "^1.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-imask": "^7.6.1", "react-router-dom": "^7.4.0", "react-scripts": "5.0.0", "react-transition-group": "^4.4.5", "react-use-measure": "^2.1.1", "single-spa-react": "^6.0.2", "sonner": "^2.0.1", "styled-components": "5.3.11", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "ua-parser-js": "^2.0.0", "use-debounce": "^10.0.0", "vaul": "^1.1.2", "web-vitals": "^2.1.0", "zod": "^3.23.8", "zustand": "^4.4.7"}, "scripts": {"postinstall": "patch-package", "build": "rsbuild build", "serve": "serve -s build -l 8080", "test": "jest", "eject": "react-scripts eject", "version-dev": "npm version prerelease --preid=alpha --no-git-tag-version --no-commit-hooks", "version-stg": "npm version prerelease --preid=beta --no-git-tag-version --no-commit-hooks", "sync_local_image_res": "ts-node scripts/generate_resource_res.ts local", "sync_cdn_image_res": "ts-node scripts/generate_resource_res.ts", "sync_cdn_image_res_all_update": "ts-node scripts/generate_resource_res.ts all update", "compress_imgs": "ts-node scripts/compress_images.ts", "test:ci": "node --expose-gc $(yarn bin jest)  --env=jsdom --runInBand --logHeapUsage --config ./jest.config.js --collectCoverage --coverageDirectory=\\\"./coverage\\\" --ci --reporters=default --reporters=jest-junit --watchAll=false && yarn run tsc", "test:local": "node --expose-gc $(yarn bin jest)  --env=jsdom  --maxWorkers=50% --logHeapUsage --config ./jest.config.js --collectCoverage --coverageDirectory=\\\"./coverage\\\" --ci --reporters=default --reporters=jest-junit --watchAll=false && yarn run tsc", "test:watch": "jest --env=jsdom --coverage --verbose=false --watch", "test:report-slow": "jest --env=jsdom --maxWorkers=50% --reporters=jest-slow-test-reporter", "sia": "yarn sync_cdn_image_res_all_update", "sil": "yarn sync_local_image_res", "client:rsdoctor": "rsdoctor analyze --profile .next/.rsdoctor/manifest.json", "dev": "rsbuild dev", "preview": "rsbuild preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off"}}, "browserslist": {"production": ["Android >= 4", "iOS >= 8", "ChromeAndroid >= 1", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@rsbuild/core": "^1.2.15", "@rsbuild/plugin-react": "^1.1.1", "@rsbuild/plugin-sass": "^1.2.2", "@rsdoctor/webpack-plugin": "^0.4.13", "@swc/core": "^1.11.11", "@swc/jest": "^0.2.37", "@tailwindcss/line-clamp": "^0.4.4", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@testing-library/react-hooks": "^8.0.1", "@types/jest": "^29.5.14", "@types/js-sdk": "^0.0.123", "@types/loadable__component": "^5.13.9", "@types/lodash.debounce": "^4.0.9", "@types/lodash.get": "^4.4.9", "@types/lodash.isnumber": "^3.0.9", "@types/node": "^16.7.13", "@types/react": "18", "@types/react-dom": "18", "@types/styled-components": "^5.1.34", "@types/ua-parser-js": "^0.7.39", "autoprefixer": "^10.4.16", "basic-ftp": "^5.0.5", "eslint-config-prettier": "9.1.0", "eslint-plugin-testing-library": "^5.0.5", "fake-indexeddb": "^6.0.0", "http-proxy-middleware": "^2.0.6", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "patch-package": "^8.0.0", "postcss": "^8.4.33", "postinstall-postinstall": "^2.1.0", "sass": "^1.69.7", "sass-loader": "^13.3.3", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "volta": {"node": "18.20.5", "yarn": "1.19.0"}}