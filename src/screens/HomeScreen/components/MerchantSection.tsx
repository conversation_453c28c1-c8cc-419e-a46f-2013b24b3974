import { Templates } from "@/types/promotion";
import R18RewardVoucherV2 from "@/components/ModuleFederation/R18RewardVoucherV2";
import R18ProductPageSlider from "@/components/ModuleFederation/R18ProductPageSlider";
import { useTracking } from "@/hooks/useTracking";
import { HomeScreen, ScreenId } from "../HomeTrackingId";
import { accountStore } from "@/store/accountStore";
import { INSTALLMENT_PRODUCT_INVENTORY_ID, INSTALLMENT_REWARD_INVENTORY_ID } from "@/screens/OnboardingScreen/constant";

export const MerchantSection = () => {
  const trackEvent = useTracking(ScreenId.HomeScreen).trackEvent;
  const installment_account_status = accountStore.getState()?.isBound ? "bound" : "unbound";

  const onLoadProductPageSlider = (data: any) => {
    console.log("onLoadProductPageSlider", data);
    trackEvent(HomeScreen.LoadMerchantComponent, {
      installment_account_status,
    });
  };

  const onLoadRewardVoucherV2 = (data: any) => {
    console.log("onLoadRewardVoucherV2", data);
    trackEvent(HomeScreen.LoadVoucherComponent, {
      installment_account_status,
    });
  };

  return (
    <>
    <section>
      <R18RewardVoucherV2
        className="[&>div>section>div>h3]:!text-base w-full mb-3 overflow-hidden empty:hidden"
        reward_inventory_id={"osc_retail_mart_4"}
        onLoaded={onLoadRewardVoucherV2}
        hasSkeleton={false}
      />
      <R18ProductPageSlider
        request={{
          inventory_id: "Promotion_Hub_Top",
          extra_infos: {
            position: "installment_homepage",
          },
        }}
        className="[&>div>div>h3]:!text-base [&>div>div>div]:!text-base empty:hidden mx-4 mb-4 p-4 pt-4.5 bg-white rounded-extra-lg"
        onLoaded={onLoadProductPageSlider}
        template={Templates.LIST11}
        containerPadding={16}
        hasSkeleton={false}
      />
      </section>
    </>
  );
};
