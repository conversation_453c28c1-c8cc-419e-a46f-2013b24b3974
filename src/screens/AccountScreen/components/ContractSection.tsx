import { format } from "date-fns";
import { memo, useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

import { ContractResponse } from "@/api/getContract";
import { Button } from "@/components/ui/Button";
import { Divider } from "@/components/ui/Divider";
import { Skeleton } from "@/components/ui/Skeleton";
import { accountStore } from "@/store/accountStore";
import { formatCurrency } from "@/utils/formatCurrency";

import { AccountScreenId } from "../AccountScreenTrackingId";
import { GlobalDrawer } from "@/components/GlobalDrawer";
import { LoadingIcon } from "@/components/ui/LoadingIcon";
import { PdfFileViewer } from "@/components/PdfFileViewer";

const ContractSection = ({ onTrack }: { onTrack?: (eventId: string, metadata?: Record<string, any>) => void }) => {
  const accountInfo = accountStore(state => state.accountInfo);
  const onboardingId = accountStore(state => state.permissionInfo?.bind_info?.onboarding_id);
  const contractInfo = accountStore(state => state.contractInfo);
  const getContractInfo = accountStore.getState().getContractInfo;
  const [loading, setLoading] = useState(false);
  const [showContract, setShowContract] = useState(false);

  const handleOpenContract = useCallback(async () => {
    if (loading) {
      return;
    }
    onTrack?.(AccountScreenId.ClickViewContract);
    try {
      setLoading(true);
      if (!onboardingId) {
        throw "Empty OnboaringId";
      }
      const contractInfoResp: ContractResponse | undefined = await getContractInfo(onboardingId);
      if (contractInfoResp?.signed_contract_url) {
        setShowContract(true);
      } else {
        toast.message("Hợp đồng chưa có sẵn, vui lòng thử lại sau.");
      }
    } catch {
      toast.error("Đã có lỗi khi lấy thông tin hợp đồng, vui lòng thử lại sau.");
    } finally {
      setLoading(false);
    }
  }, [contractInfo, loading]);

  useEffect(() => {
    if (!showContract) {
      return;
    }

    GlobalDrawer.open({
      title: "Hợp đồng",
      onClose: () => {
        setShowContract(false);
        GlobalDrawer.close();
      },
      children: (
        <div className="overflow-y-auto w-full h-full">
          {contractInfo?.signed_contract_url ? (
            <PdfFileViewer url={contractInfo.signed_contract_url} height="calc(100vh - 160px)" />
          ) : (
            <div className="p-4 text-center h-full w-full flex justify-center items-center">
              Không tải được thông tin, vui lòng thử lại sau.
            </div>
          )}
        </div>
      ),
    });
  }, [showContract]);

  return (
    <>
      <ul className="bg-white rounded-lg text-base">
        <li className="pt-4 space-y-2">
          <div className="px-4 flex justify-between items-start">
            <h3 className="text-lead font-bold">Thông tin hợp đồng</h3>
            {onboardingId ? (
              <Button onClick={handleOpenContract} variant="link" className="p-0 h-auto text-base font-normal">
                {loading ? <LoadingIcon className="text-blue-500 duration-300" /> : <p>Xem hợp đồng</p>}
              </Button>
            ) : null}
          </div>
          <Divider type="dot" className="px-4" />
        </li>
        <li className="p-4 flex justify-between items-center">
          <span className="text-dark-300">Họ và tên</span>
          <span>
            {accountInfo && accountInfo?.partner_account_name ? (
              accountInfo?.partner_account_name
            ) : (
              <Skeleton className="w-[72px] h-[17px] my-0.5" />
            )}
          </span>
        </li>
        <li className="p-4 flex justify-between items-center">
          <span className="text-dark-300">Số tài khoản</span>
          <span>
            {accountInfo && accountInfo?.partner_account_number ? (
              accountInfo?.partner_account_number
            ) : (
              <Skeleton className="w-[72px] h-[17px] my-0.5" />
            )}
          </span>
        </li>
        <li className="p-4 flex justify-between items-center">
          <span className="text-dark-300">Hạn mức được cấp</span>
          <span>
            {accountInfo && accountInfo?.installment_balance ? (
              formatCurrency(Number(accountInfo?.installment_limit))
            ) : (
              <Skeleton className="w-[72px] h-[17px] my-0.5" />
            )}
          </span>
        </li>
        <li className="p-4 flex justify-between items-center">
          <span className="text-dark-300">Ngày hiệu lực</span>
          <span>
            {accountInfo && accountInfo?.created_at ? (
              format(new Date(accountInfo.created_at), "dd/MM/yyyy")
            ) : (
              <Skeleton className="w-[142px] h-4 my-0.5" />
            )}
          </span>
        </li>
        <li className="p-4 flex justify-between items-center">
          <span className="text-dark-300">Ngày sao kê</span>
          <span>
            {accountInfo && accountInfo?.installment_term?.stmt_incur_date_text ? (
              accountInfo?.installment_term?.stmt_incur_date_text
            ) : (
              <Skeleton className="w-[142px] h-4 my-0.5" />
            )}
          </span>
        </li>
        <li className="p-4 flex justify-between items-center">
          <span className="text-dark-300">Ngày đến hạn thanh toán</span>
          <span>
            {accountInfo && accountInfo?.installment_term?.stmt_due_date_text ? (
              accountInfo?.installment_term?.stmt_due_date_text
            ) : (
              <Skeleton className="w-[82px] h-4 my-0.5" />
            )}
          </span>
        </li>
        {/* <li className="p-4 flex justify-between items-center">
          <span>Phí chuyển đổi</span>
          <span>
            {accountInfo && accountInfo?.installment_term?.feeExplanation ? (
              accountInfo?.installment_term?.feeExplanation
            ) : (
              <Skeleton className="w-[122px] h-[16px] my-0.5" />
            )}
          </span>
        </li> */}
      </ul>
    </>
  );
};

export default memo(ContractSection);
