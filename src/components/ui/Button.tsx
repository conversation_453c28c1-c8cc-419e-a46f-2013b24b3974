import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "../../lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-base transition-colors disabled:pointer-events-none disabled:opacity-50 active:opacity-50 active:brightness-125",
  {
    variants: {
      variant: {
        default: "bg-primary text-white",
        primary: "bg-primary text-white",
        destructive:
          "bg-destructive text-destructive-foreground",
        outlined:
          "border border-solid border-primary text-primary",
        secondary:
          "bg-blue-25 text-black",
        ghost: "hover:bg-accent",
        link: "text-primary underline-offset-4",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-12 rounded-md px-8",
        icon: "h-10 w-10",
        tiny: "px-3 py-2"
      },
      animation: {
        default: "",
        scale: "active:scale-[98%] duration-300 active:brightness-110",
        brighter: "duration-300 active:brightness-110"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, animation, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, animation, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
